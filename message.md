/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveNavigator.swift:85 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveNavigator.swift:86 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveNavigator.swift:87 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:93 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:93 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:93 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:93 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:93 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveNavigator.swift:135 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveNavigator.swift:136 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveNavigator.swift:137 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:211 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:211 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:212 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:212 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:216 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:216 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:254 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:254 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:255 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift:255 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveHandler.swift:414 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveHandler.swift:414 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveHandler.swift:415 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveHandler.swift:415 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveHandler.swift:416 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/MoveHandler.swift:416 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:451 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:451 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:473 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:514 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
/Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/GameSession.swift:514 Publishing changes from within view updates is not allowed, this will cause undefined behavior.
请给出修改意见，我确认后再执行修改
<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "05922273-AD4E-4F7D-942C-3F18A00E46E6"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BF1D612E-140E-4888-BB16-D4C062CC6B14"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "../../Library/Developer/Xcode/DerivedData/MacChessBase-dphbbtmczcqvpkfdtqorpxxwqgxe/SourcePackages/checkouts/chesskit-swift/Sources/ChessKit/MoveTree/MoveTree.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "318"
            endingLineNumber = "318"
            landmarkName = "pgn(for:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3EE016CD-A78C-4A95-9747-FA9AF28360A9"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MacChessBase/ChessGameViewModel.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "815"
            endingLineNumber = "815"
            landmarkName = "showVariationSelectionDialog(mainLineIndex:variationIndices:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "ACFF8140-02EC-4BD6-B8C5-F69505ED7158"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MacChessBase/VariationCreationDialogView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "183"
            endingLineNumber = "183"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "51BED37F-DD3A-4AB8-9D64-7CDC1FE2AD1C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MacChessBase/ChessBoardView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "98"
            endingLineNumber = "98"
            landmarkName = "boardView"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "070285D9-DAB8-43F4-B379-CE8D07965C71"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MacChessBase/ChessKitExtensions.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "83"
            endingLineNumber = "83"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C8A739CC-9360-4CEB-B455-EA0DE10B9E5C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MacChessBaseTests/ChessGameViewModelTests.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "12"
            endingLineNumber = "12"
            landmarkName = "ChessGameViewModelTests"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "8E7B9654-9F0C-403B-87E0-35904B1126B3"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MacChessBaseTests/GameSessionUndoRedoTests.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "119"
            endingLineNumber = "119"
            landmarkName = "testDeleteMoveUndoRedo()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>

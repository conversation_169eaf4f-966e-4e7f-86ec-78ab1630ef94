<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1620"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      buildArchitectures = "Automatic">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "8E61480D2DEAB78F0032F0FE"
               BuildableName = "MacChessBase.app"
               BlueprintName = "MacChessBase"
               ReferencedContainer = "container:MacChessBase.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      shouldAutocreateTestPlan = "YES">
      <Testables>
         <TestableReference
            skipped = "NO"
            parallelizable = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "8E6148202DEAB7930032F0FE"
               BuildableName = "MacChessBaseTests.xctest"
               BlueprintName = "MacChessBaseTests"
               ReferencedContainer = "container:MacChessBase.xcodeproj">
            </BuildableReference>
            <SkippedTests>
               <Test
                  Identifier = "ChessGameViewModelTests">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testFlipBoard()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testInitialState()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testLoadGameFromFEN()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testLoadGameFromPGN()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testMakeCapture()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testMakeInvalidMove()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testMakeValidMove()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testMoveNavigation()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testNewGame()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testSquareSelection()">
               </Test>
               <Test
                  Identifier = "ChessGameViewModelTests/testSquareSelectionWithMove()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testAllSquareTransformations()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testBoardEdgeSquares()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testBoardSizeCalculations()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testBoardSizeCalculationsWithDifferentSizes()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testCoordinateRangeValidation()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testCoordinateTransformPerformance()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testGameExtensions()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testMoveExtensions()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testNegativeCoordinates()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testPieceExtensions()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testPointOutsideBoard()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testPointToSquarePerformance()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testPointToSquareTransform()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testPointToSquareTransformFlipped()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testPositionExtensions()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testSquareExtensions()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testSquareToCoordinateTransform()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testSquareToCoordinateTransformFlipped()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testTransformationConsistency()">
               </Test>
               <Test
                  Identifier = "ChessKitExtensionsTests/testVeryLargeCoordinates()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testAnalysisPerformance()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testAnalyzeComplexPosition()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testAnalyzeCustomPosition()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testAnalyzeStandardPosition()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testClearAnalysis()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineDepthSetting()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineHashSizeSetting()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineMemoryUsage()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineMultiPVSetting()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineOptions()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineRestart()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineShutdown()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineStartup()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineStartupFailure()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineThreadsSetting()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testEngineVersion()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testInvalidPositionAnalysis()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testMultiPVAnalysis()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testMultiplePositionAnalysisPerformance()">
               </Test>
               <Test
                  Identifier = "EngineManagerTests/testPauseAndResumeAnalysis()">
               </Test>
               <Test
                  Identifier = "FENValidationTests">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testCastlingRightsLogicValidation()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testCheckValidation()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testComplexFENValidationPerformance()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testComplexInvalidPositions()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testComplexValidPositions()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testEnPassantLogicValidation()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testFENValidationPerformance()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testInvalidActiveColor()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testInvalidCastlingRights()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testInvalidEnPassant()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testInvalidFENStructure()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testInvalidMoveCounters()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testInvalidPiecePlacement()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testKingCountValidation()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testPawnPlacementValidation()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testPieceCountValidation()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testValidActiveColor()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testValidCastlingRights()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testValidEnPassant()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testValidMoveCounters()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testValidPiecePlacement()">
               </Test>
               <Test
                  Identifier = "FENValidationTests/testValidStandardFEN()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testConcurrentSessionOperations()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testCreateMultipleSessions()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testCreateNewSession()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testInitialState()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testRemoveActiveSession()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testRemoveLastSession()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testRemoveSession()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testRenameSession()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testSelectNonexistentSession()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testSelectSession()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testSessionCreationPerformance()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testSessionGameState()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testSessionIndependence()">
               </Test>
               <Test
                  Identifier = "GameSessionManagerTests/testSessionSwitchingPerformance()">
               </Test>
               <Test
                  Identifier = "MacChessBaseTests">
               </Test>
               <Test
                  Identifier = "MacChessBaseTests/example()">
               </Test>
               <Test
                  Identifier = "PerformanceTests">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testBoardCoordinateTransformationPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testComplexGamePerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testFENParsingPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testFENValidationPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testGameCreationPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testLegalMovesGenerationPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testMemoryUsageWithLargeGames()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testMoveExecutionPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testPGNGenerationPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testPGNParsingPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testPositionEvaluationPerformance()">
               </Test>
               <Test
                  Identifier = "PerformanceTests/testRapidGameCreationAndDestruction()">
               </Test>
            </SkippedTests>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "8E61482A2DEAB7930032F0FE"
               BuildableName = "MacChessBaseUITests.xctest"
               BlueprintName = "MacChessBaseUITests"
               ReferencedContainer = "container:MacChessBase.xcodeproj">
            </BuildableReference>
            <SkippedTests>
               <Test
                  Identifier = "MacChessBaseUITests">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testAccessibilityElements()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testBoardRenderingPerformance()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testChessBoardInteraction()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testEditMenu()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testEngineAnalysisView()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testExample()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testFileMenu()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testGameMenu()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testGameSessionSidebar()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testInvalidPGNImport()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testKeyboardShortcuts()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testLaunchPerformance()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testMoveNotationView()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testMultipleWindows()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testNavigationPerformance()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testPositionEditor()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITests/testWindowResize()">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITestsLaunchTests">
               </Test>
               <Test
                  Identifier = "MacChessBaseUITestsLaunchTests/testLaunch()">
               </Test>
            </SkippedTests>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8E61480D2DEAB78F0032F0FE"
            BuildableName = "MacChessBase.app"
            BlueprintName = "MacChessBase"
            ReferencedContainer = "container:MacChessBase.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8E61480D2DEAB78F0032F0FE"
            BuildableName = "MacChessBase.app"
            BlueprintName = "MacChessBase"
            ReferencedContainer = "container:MacChessBase.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>

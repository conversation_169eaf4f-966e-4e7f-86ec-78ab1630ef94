{"permissions": {"allow": ["Bash(find /Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests -name \"*MoveTree*\" -type f)", "Bash(find /Users/<USER>/Projects/MacChessBase/chesskit-swift/Tests -name \"*.swift\" -exec grep -l \"MoveTreeEditingAdvanced\\|MoveTreeEditing.*Advanced\" {} ;)", "Bash(find /Users/<USER>/Projects/MacChessBase -name \"*.xcassets\" -type d)", "Bash(grep -n \"pieceImageName\" /Users/<USER>/Projects/MacChessBase/MacChessBase/Views/OptimizedChessSquareView.swift)", "Bash(grep:*)", "Bash(rg:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(swift test:*)", "Bash(xcodebuild:*)", "Bash(cd /Users/<USER>/Projects/MacChessBase)", "Bash(grep -n \"init(\" MacChessBase/ViewModels/ChessGameViewModel.swift)", "Bash(find MacChessBase -name \"*.swift\" -exec grep -l \"game\\.make\\|game\\.delete\\|game\\.promote\\|game\\.overwrite\" {} ;)", "Bash(grep -n \"game\\.make\\|game\\.delete\\|game\\.promote\\|game\\.overwrite\" MacChessBase/ViewModels/ChessGameViewModel.swift)", "Bash(grep -n \"deleteMove\\|promoteVariation\\|promoteToMainVariation\" MacChessBase/Views/NotationView.swift)", "Bash(find MacChessBase -name \"*.swift\" -exec grep -l \"deleteMove\\|promoteVariation\\|promoteToMainVariation\" {} ;)", "Bash(find MacChessBase -name \"*.swift\" -exec grep -l \"Menu\\|NSMenuItem\" {} ;)", "Bash(grep -n \"NotificationCenter\\|onReceive\" MacChessBase/ViewModels/ChessGameViewModel.swift)", "Bash(grep -n \"NotificationCenter\" MacChessBase/Views/ChessGameView.swift)", "Bash(find MacChessBase -name \"*.swift\" -exec grep -l \"onReceive.*NotificationCenter\\|NotificationCenter.*addObserver\" {} ;)", "Bash(cd /Users/<USER>/Projects/MacChessBase/MacChessBase)", "Bash(grep -n \"game\\.make(\" ViewModels/ChessGameViewModel.swift)", "Bash(grep -n \"game\\.delete(\" ViewModels/ChessGameViewModel.swift)", "Bash(grep -n \"game\\.overwrite(\" ViewModels/ChessGameViewModel.swift)", "Bash(grep -n \"game\\.promote(\" ViewModels/ChessGameViewModel.swift)", "Bash(grep -n -A 10 \"game\\.promote(index: index)\" /Users/<USER>/Projects/MacChessBase/MacChessBase/ViewModels/ChessGameViewModel.swift)", "Bash(grep -n \"onAppear\\|onReceive\" /Users/<USER>/Projects/MacChessBase/MacChessBase/Views/ChessGameView.swift)", "Bash(cd /Users/<USER>/Projects/MacChessBase/chesskit-swift)", "Bash(swift build)", "Bash(grep -A 10 \"public struct AddMoveUndoState\" /Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift)", "Bash(grep -A 10 \"public struct DeleteMoveUndoState\" /Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift)", "Bash(grep -A 15 -B 2 \"Index.*:\" /Users/<USER>/Projects/MacChessBase/chesskit-swift/Sources/ChessKit/UndoState.swift)"], "deny": []}}
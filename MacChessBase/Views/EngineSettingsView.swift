//
//  EngineSettingsView.swift
//  MacChessBase
//
//  Created on 2025/6/8.
//

import SwiftUI

/// A view for configuring engine settings
struct EngineSettingsView: View {
    @ObservedObject var engineManager: EngineManager
    let chessGameViewModel: ChessGameViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var isRestarting = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            HStack {
                Text("Engine Settings")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if isRestarting {
                    HStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                            .scaleEffect(0.8)
                        Text("Restarting engine...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Button("Done") {
                    Task {
                        isRestarting = true
                        await restartEngineIfNeeded()
                        isRestarting = false
                        dismiss()
                    }
                }
                .buttonStyle(.borderedProminent)
                .disabled(isRestarting)
            }
            .padding(.bottom, 8)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    // Analysis Settings
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Analysis Settings")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        // Number of variations
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Number of Variations")
                                    .font(.subheadline)
                                Spacer()
                                Text("\(engineManager.maxLines)")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Slider(
                                value: Binding(
                                    get: { Double(engineManager.maxLines) },
                                    set: { engineManager.maxLines = Int($0) }
                                ),
                                in: 1...5,
                                step: 1
                            )
                            
                            Text("Number of candidate moves to analyze simultaneously")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        // Analysis mode
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Analysis Mode")
                                .font(.subheadline)
                            
                            Picker("Analysis Mode", selection: $engineManager.useFixedDepth) {
                                Text("Infinite Analysis").tag(false)
                                Text("Fixed Depth").tag(true)
                            }
                            .pickerStyle(.segmented)
                            
                            if engineManager.useFixedDepth {
                                HStack {
                                    Text("Search Depth")
                                        .font(.subheadline)
                                    Spacer()
                                    Text("\(engineManager.maxDepth)")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                }
                                
                                Slider(
                                    value: Binding(
                                        get: { Double(engineManager.maxDepth) },
                                        set: { engineManager.maxDepth = Int($0) }
                                    ),
                                    in: 5...30,
                                    step: 1
                                )
                                
                                Text("Maximum search depth in plies")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            } else {
                                Text("Engine will analyze continuously until stopped")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.controlBackgroundColor))
                    )
                    
                    // Engine Performance Settings
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Performance Settings")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        // Hash table size
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Hash Table Size")
                                    .font(.subheadline)
                                Spacer()
                                Text("\(engineManager.hashSizeMB) MB")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Slider(
                                value: Binding(
                                    get: { Double(engineManager.hashSizeMB) },
                                    set: { engineManager.hashSizeMB = Int($0) }
                                ),
                                in: 16...1024,
                                step: 16
                            )
                            
                            Text("Memory allocated for hash table (applies on engine restart)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        // Thread count
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("CPU Threads")
                                    .font(.subheadline)
                                Spacer()
                                Text("\(engineManager.threadsCount)")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Slider(
                                value: Binding(
                                    get: { Double(engineManager.threadsCount) },
                                    set: { engineManager.threadsCount = Int($0) }
                                ),
                                in: 1...Double(ProcessInfo.processInfo.processorCount),
                                step: 1
                            )
                            
                            Text("Number of CPU threads to use (max: \(ProcessInfo.processInfo.processorCount))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.controlBackgroundColor))
                    )
                    
                    // Engine Information
                    VStack(alignment: .leading, spacing: 16) {
                        Text("Engine Information")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        if let engineInfo = engineManager.engineInfo {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("Name:")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text(engineInfo.name)
                                        .font(.subheadline)
                                }
                                
                                if !engineInfo.version.isEmpty {
                                    HStack {
                                        Text("Version:")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        Spacer()
                                        Text(engineInfo.version)
                                            .font(.subheadline)
                                    }
                                }
                            }
                        } else {
                            Text("Engine not running")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("Status:")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            Spacer()
                            HStack {
                                Circle()
                                    .fill(engineManager.state != .stopped ? .green : .red)
                                    .frame(width: 8, height: 8)
                                Text(engineManager.state != .stopped ? "Running" : "Stopped")
                                    .font(.subheadline)
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.controlBackgroundColor))
                    )
                    
                    // Reset to defaults
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Reset Settings")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Button(action: resetToDefaults) {
                            HStack {
                                Image(systemName: "arrow.counterclockwise")
                                Text("Reset to Defaults")
                            }
                        }
                        .buttonStyle(.bordered)
                        
                        Text("This will reset all engine settings to their default values")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.controlBackgroundColor))
                    )
                }
            }
        }
        .padding()
        .frame(width: 400, height: 600)
    }
    
    private func resetToDefaults() {
        engineManager.maxDepth = 20
        engineManager.maxLines = 2
        engineManager.hashSizeMB = 128
        engineManager.threadsCount = 1
        engineManager.useFixedDepth = false
    }
    
    private func restartEngineIfNeeded() async {
        // Track if engine was analyzing before restart
        let wasAnalyzing = engineManager.state == .analyzing
        let currentPosition = chessGameViewModel.board.position
        
        // Always restart the engine if it's running to apply all settings changes
        // This ensures hash table size, thread count, and other parameters are properly applied
        if engineManager.state != .stopped {
            await engineManager.stopEngine()
            await engineManager.startEngine()
            
            // If engine was analyzing before the restart, automatically start analysis again
            if wasAnalyzing {
                await engineManager.analyzePosition(currentPosition)
                print("✅ Engine analysis automatically restarted after settings change")
            }
        }
    }
}

#Preview {
    let vm = ChessGameViewModel()
    return EngineSettingsView(engineManager: vm.engineManager, chessGameViewModel: vm)
}

//
//  ChessClockView.swift
//  MacChessBase
//
//  Created on 2025/7/2.
//

import SwiftUI
import ChessKit
import Foundation
import AppKit

/// A view that displays chess clock information for both players
struct ChessClockView: View {
    @ObservedObject var viewModel: ChessGameViewModel
    let playerColor: Piece.Color
    
    // FIDE service for fetching federation info
    @StateObject private var fideService = FIDEService()
    @State private var playerFederation: String? = nil
    @State private var playerPhotoData: Data? = nil
    
    var body: some View {
        HStack(spacing: 12) {
            // Game result for this player
            if !gameResult.isEmpty {
                Text(gameResult)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                    .frame(minWidth: 20)
                
                // Separator line
                Divider()
                    .frame(height: 20)
            }
            
            // Player information
            HStack(spacing: 8) {
                // Team (if available)
                if !playerTeam.isEmpty {
                    Text(playerTeam)
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
                
                // Federation/Country flag (if available)
                if let federation = playerFederation {
                    HStack(spacing: 4) {
                        // Convert country name to federation code first, then get flag
                        if let federationCode = fideService.countryNameToFederationCode(federation),
                           let flag = fideService.federationCodeToFlag(federationCode) {
                            Text(flag)
                                .font(.title2)
                        }
                    }
                }

                // Player photo (if available and valid)
                if let photoData = playerPhotoData,
                   !photoData.isEmpty,
                   let nsImage = NSImage(data: photoData),
                   nsImage.isValid {
                    Image(nsImage: nsImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 32, height: 32)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                }

                // Title (if available)
                if !playerTitle.isEmpty {
                    Text(playerTitle)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.orange)
                }

                // Player name
                Text(playerName)
                    .font(.headline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                // Elo rating (if available)
                if !playerElo.isEmpty {
                    Text(playerElo)
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
            }
            
            Spacer()
            
            // Time information (if available)
            if let timeInfo = currentTimeInfo {
                HStack(spacing: 8) {
                    // Remaining time
                    if let remainingTime = timeInfo.remainingTime {
                        HStack(spacing: 4) {
                            Image(systemName: "clock")
                                .foregroundColor(.secondary)
                                .font(.caption)
                            Text(formatTime(remainingTime))
                                .font(.system(.body, design: .monospaced))
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                        }
                    }
                    
                    // Time spent on this move
                    if let timeSpent = timeInfo.timeSpent {
                        HStack(spacing: 4) {
                            Image(systemName: "timer")
                                .foregroundColor(.orange)
                                .font(.caption)
                            Text(formatTime(timeSpent))
                                .font(.system(.body, design: .monospaced))
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Rectangle()
                        .fill(timeInfoBackgroundColor)
                        .stroke(timeInfoBorderColor, lineWidth: isCurrentPlayerTurn ? 2 : 1)
                )
                .animation(.easeInOut(duration: 0.2), value: isCurrentPlayerTurn)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            UnevenRoundedRectangle(
                cornerRadii: cornerRadiiForPlayer,
                style: .continuous
            )
            .fill(Color(NSColor.controlBackgroundColor).opacity(0.2))
            .stroke(Color.secondary.opacity(0.1), lineWidth: 1)
        )
        .onAppear {
            fetchFederationIfNeeded()
        }
        .onChange(of: playerFideId) { _, _ in
            fetchFederationIfNeeded()
        }
    }
    
    // MARK: - Computed Properties
    
    /// Get the player name for the specified color
    private var playerName: String {
        guard let session = viewModel.session else {
            return playerColor == .white ? "White" : "Black"
        }
        
        switch playerColor {
        case .white:
            let name = session.game.metadata.white
            return name.isEmpty ? "White" : name
        case .black:
            let name = session.game.metadata.black
            return name.isEmpty ? "Black" : name
        }
    }
    
    /// Get the player team for the specified color
        private var playerTeam: String {
            guard let session = viewModel.session else { return "" }
            
            switch playerColor {
            case .white:
                return session.game.metadata.whiteTeam
            case .black:
                return session.game.metadata.blackTeam
            }
        }
        
        /// Get the player title for the specified color
        private var playerTitle: String {
            guard let session = viewModel.session else { return "" }
            
            switch playerColor {
            case .white:
                return session.game.metadata.whiteTitle
            case .black:
                return session.game.metadata.blackTitle
            }
        }
        
        /// Get the player Elo rating for the specified color
        private var playerElo: String {
            guard let session = viewModel.session else { return "" }
            
            switch playerColor {
            case .white:
                return session.game.metadata.whiteElo
            case .black:
                return session.game.metadata.blackElo
            }
        }
        
        /// Get the player FIDE ID for the specified color
        private var playerFideId: String {
            guard let session = viewModel.session else { return "" }
            
            switch playerColor {
            case .white:
                return session.game.metadata.whiteFideId
            case .black:
                return session.game.metadata.blackFideId
            }
        }
        
        /// Get the game result for the specified player
        private var gameResult: String {
            guard let session = viewModel.session else { return "" }
            
            let result = session.game.metadata.result
            guard !result.isEmpty && result != "*" else { return "" }
            
            // Parse results like "1-0", "0-1", "1/2-1/2"
            if result == "1-0" {
                return playerColor == .white ? "1" : "0"
            } else if result == "0-1" {
                return playerColor == .white ? "0" : "1"
            } else if result == "1/2-1/2" {
                return "½"
            }
            
            return ""
        }
    
    /// Get the current move's time information for the specified player
    private var currentTimeInfo: Move.TimeAnnotations? {
        // Check if we're at the starting position
        if viewModel.currentMoveIndex == MoveTree.minimumIndex {
            // At starting position, try to show initial time from TimeControl
            if let initialTime = initialTimeFromTimeControl {
                return Move.TimeAnnotations(remainingTime: initialTime, timeSpent: nil)
            }
            return nil
        }
        
        // Get the last move made by this player
        if let lastMoveByPlayer = getLastMoveByPlayer(playerColor) {
            let timeAnnotations = lastMoveByPlayer.positionComment.timeAnnotations
            if !timeAnnotations.isEmpty {
                return timeAnnotations
            }
        }
        
        // If no move by this player yet, but game has started, show initial time
        if let initialTime = initialTimeFromTimeControl {
            return Move.TimeAnnotations(remainingTime: initialTime, timeSpent: nil)
        }
        
        return nil
    }
    
    /// Parse initial time from the TimeControl PGN tag
    private var initialTimeFromTimeControl: String? {
        guard let session = viewModel.session else { return nil }
        
        let timeControl = session.game.metadata.timeControl.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !timeControl.isEmpty else { return nil }
        
        return parseTimeControl(timeControl)
    }
    
    /// Get the last move made by the specified player
    private func getLastMoveByPlayer(_ color: Piece.Color) -> Move? {
        let currentIndex = viewModel.currentMoveIndex
        
        guard let session = viewModel.session else { return nil }
        
        // Walk backwards from current position to find the last move by this player
        var index = currentIndex
        while index != MoveTree.minimumIndex {
            if let move = session.game.moves.getNodeMove(index: index) {
                // Determine the color of the player who made this move
                // We need to check the position before this move to see whose turn it was
                if let previousIndex = session.game.moves.previousIndex(currentIndex: index),
                   let previousPosition = session.game.positions[previousIndex] {
                    if previousPosition.sideToMove == color {
                        return move
                    }
                }
            }
            
            // Move to previous position
            if let prevIndex = session.game.moves.previousIndex(currentIndex: index) {
                index = prevIndex
            } else {
                break
            }
        }
        
        return nil
    }
    
    /// Check if it's the current player's turn
    private var isCurrentPlayerTurn: Bool {
        return viewModel.currentPlayer == playerColor
    }
    
    /// Background color for the player's clock
    private var backgroundColorForPlayer: Color {
        if isCurrentPlayerTurn {
            return playerColor == .white ? Color.blue.opacity(0.05) : Color.red.opacity(0.05)
        } else {
            return Color(NSColor.controlBackgroundColor).opacity(0.3)
        }
    }
    
    /// Background color for the time info section
    private var timeInfoBackgroundColor: Color {
        if isCurrentPlayerTurn {
            return playerColor == .white ? Color.blue.opacity(0.05) : Color.red.opacity(0.05)
        } else {
            return Color(NSColor.controlBackgroundColor).opacity(0.3)
        }
    }
    
    /// Border color for the player's clock
    private var borderColorForPlayer: Color {
        if isCurrentPlayerTurn {
            return playerColor == .white ? Color.blue : Color.red
        } else {
            return Color.secondary.opacity(0.3)
        }
    }
    
    /// Border color for the time info section
    private var timeInfoBorderColor: Color {
        if isCurrentPlayerTurn {
            return playerColor == .white ? Color.blue : Color.red
        } else {
            return Color.secondary.opacity(0.3)
        }
    }
    
    /// Corner radii for asymmetric rounded corners based on player position
    private var cornerRadiiForPlayer: RectangleCornerRadii {
        if (playerColor == .black && !viewModel.isBoardFlipped) || (playerColor == .white && viewModel.isBoardFlipped) {
            return RectangleCornerRadii(
                topLeading: 8,
                bottomLeading: 0,
                bottomTrailing: 0,
                topTrailing: 8
            )
        } else {
            return RectangleCornerRadii(
                topLeading: 0,
                bottomLeading: 8,
                bottomTrailing: 8,
                topTrailing: 0
            )
        }
    }
    
    /// Fetches federation information and photo for the player if a FIDE ID is available
    private func fetchFederationIfNeeded() {
        let fideId = playerFideId.trimmingCharacters(in: .whitespacesAndNewlines)

        // Reset federation and photo if no FIDE ID
        guard !fideId.isEmpty else {
            playerFederation = nil
            playerPhotoData = nil
            return
        }

        // Fetch federation and photo asynchronously
        Task {
            do {
                let fidePlayer = try await fideService.fetchPlayerInfo(fideId: fideId)
                let federation = fidePlayer.federation
                let photoData = validatePhotoData(fidePlayer.photoData)
                await MainActor.run {
                    playerFederation = federation
                    playerPhotoData = photoData
                }
            } catch {
                // Silently fail - federation and photo are optional information
                await MainActor.run {
                    playerFederation = nil
                    playerPhotoData = nil
                }
                #if DEBUG
                print("Failed to fetch federation and photo for FIDE ID \(fideId): \(error)")
                #endif
            }
        }
    }

    /// Validates photo data to ensure it's a valid image
    /// - Parameter photoData: The raw photo data from FIDE service
    /// - Returns: Valid photo data or nil if invalid
    private func validatePhotoData(_ photoData: Data?) -> Data? {
        guard let data = photoData, !data.isEmpty else {
            #if DEBUG
            print("📷 No photo data available")
            #endif
            return nil
        }

        // Check file size - filter out placeholder/default images
        // Based on observation: invalid images are around 1122 bytes, valid ones are much larger (e.g., 449659 bytes)
        let minValidSize = 5000 // 5KB minimum for a real photo
        guard data.count >= minValidSize else {
            #if DEBUG
            print("📷 Photo data too small (\(data.count) bytes) - likely a placeholder image")
            #endif
            return nil
        }

        // Try to create NSImage to validate the data
        guard let nsImage = NSImage(data: data), nsImage.isValid else {
            #if DEBUG
            print("📷 Invalid photo data - cannot create NSImage (size: \(data.count) bytes)")
            #endif
            return nil
        }

        // Check if image has valid dimensions
        let imageSize = nsImage.size
        guard imageSize.width > 0 && imageSize.height > 0 else {
            #if DEBUG
            print("📷 Invalid photo dimensions: \(imageSize)")
            #endif
            return nil
        }

        // Additional check: ensure the image is reasonably sized (not too small)
        let minDimension: CGFloat = 32 // Minimum 32x32 pixels
        guard imageSize.width >= minDimension && imageSize.height >= minDimension else {
            #if DEBUG
            print("📷 Photo dimensions too small: \(imageSize) - likely a placeholder")
            #endif
            return nil
        }

        #if DEBUG
        print("✅ Valid photo found - size: \(imageSize), data: \(data.count) bytes")
        #endif

        return data
    }

    /// Format time string for display
    private func formatTime(_ timeString: String) -> String {
        // Remove any extra formatting and ensure consistent display
        let cleanTime = timeString.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // If it's already in HH:MM:SS format, return as is
        if cleanTime.matches(pattern: #"^\d{1,2}:\d{2}:\d{2}$"#) {
            return cleanTime
        }
        
        // If it's in MM:SS format, add hour
        if cleanTime.matches(pattern: #"^\d{1,2}:\d{2}$"#) {
            return "0:\(cleanTime)"
        }
        
        // If it's just seconds, format properly
        if let seconds = Int(cleanTime) {
            let hours = seconds / 3600
            let minutes = (seconds % 3600) / 60
            let secs = seconds % 60
            return String(format: "%d:%02d:%02d", hours, minutes, secs)
        }
        
        // Fallback: return as is
        return cleanTime
    }
    
    /// Parse TimeControl string and extract initial time
    /// Supports formats like: "1800+30", "90+30", "1800", "3000+30", "5400+15"
    /// Returns formatted time string like "01:30:00"
    private func parseTimeControl(_ timeControl: String) -> String? {
        let clean = timeControl.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Handle various TimeControl formats
        // Format 1: "minutes+seconds" with m/s suffixes (e.g., "90m+30s")
        if let match = clean.firstMatch(of: /^(\d+)m\+(\d+)s$/) {
            let minutes = Int(match.1) ?? 0
            let incrementSeconds = Int(match.2) ?? 0
            let totalSeconds = minutes * 60 + incrementSeconds
            return formatSecondsToTime(totalSeconds)
        }
        
        // Format 2: Just minutes with m suffix (e.g., "90m")
        if let match = clean.firstMatch(of: /^(\d+)m$/) {
            let minutes = Int(match.1) ?? 0
            let totalSeconds = minutes * 60
            return formatSecondsToTime(totalSeconds)
        }
        
        // Format 3: "hours+minutes" with h/m suffixes (e.g., "1h+30m")
        if let match = clean.firstMatch(of: /^(\d+)h\+(\d+)m$/) {
            let hours = Int(match.1) ?? 0
            let minutes = Int(match.2) ?? 0
            let totalSeconds = hours * 3600 + minutes * 60
            return formatSecondsToTime(totalSeconds)
        }
        
        // Format 4: Just hours with h suffix (e.g., "2h")
        if let match = clean.firstMatch(of: /^(\d+)h$/) {
            let hours = Int(match.1) ?? 0
            let totalSeconds = hours * 3600
            return formatSecondsToTime(totalSeconds)
        }
        
        // Format 5: "seconds+increment" (e.g., "1800+30")
        if let match = clean.firstMatch(of: /^(\d+)\+(\d+)$/) {
            let seconds = Int(match.1) ?? 0
            let incrementSeconds = Int(match.2) ?? 0
            let totalSeconds = seconds + incrementSeconds
            return formatSecondsToTime(totalSeconds)
        }
        
        // Format 6: Just seconds (e.g., "1800")
        if let seconds = Int(clean) {
            return formatSecondsToTime(seconds)
        }
        
        // Format 7: "minutes:seconds+increment" (e.g., "90:00+30")
        if let match = clean.firstMatch(of: /^(\d+):(\d+)\+(\d+)$/) {
            let minutes = Int(match.1) ?? 0
            let seconds = Int(match.2) ?? 0
            let incrementSeconds = Int(match.3) ?? 0
            let totalSeconds = minutes * 60 + seconds + incrementSeconds
            return formatSecondsToTime(totalSeconds)
        }
        
        // Format 8: "hours:minutes:seconds+increment" (e.g., "1:30:00+30")
        if let match = clean.firstMatch(of: /^(\d+):(\d+):(\d+)\+(\d+)$/) {
            let hours = Int(match.1) ?? 0
            let minutes = Int(match.2) ?? 0
            let seconds = Int(match.3) ?? 0
            let incrementSeconds = Int(match.4) ?? 0
            let totalSeconds = hours * 3600 + minutes * 60 + seconds + incrementSeconds
            return formatSecondsToTime(totalSeconds)
        }
        
        // Format 9: Already in time format "H:MM:SS" or "HH:MM:SS"
        if clean.matches(pattern: #"^\d{1,2}:\d{2}:\d{2}$"#) {
            return clean
        }
        
        return nil
    }
    
    /// Convert seconds to formatted time string "H:MM:SS"
    private func formatSecondsToTime(_ totalSeconds: Int) -> String {
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        return String(format: "%d:%02d:%02d", hours, minutes, seconds)
    }
}

// MARK: - String Extension for Pattern Matching
private extension String {
    func matches(pattern: String) -> Bool {
        return self.range(of: pattern, options: .regularExpression) != nil
    }
}

//
//  EngineAnalysisView.swift
//  MacChessBase
//
//  Created on 2025/6/8.
//

import SwiftUI
import ChessKit

/// A view that displays chess engine analysis results
struct EngineAnalysisView: View {
    @ObservedObject var engineManager: EngineManager
    let chessGameViewModel: ChessGameViewModel
    @State private var showingEngineSettings = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header
            HStack {
                Text("Engine Analysis")
                    .font(.headline)
                
                Spacer()
                
                // Engine controls
                HStack(spacing: 8) {
                    // 根据引擎状态显示不同的按钮组合
                    switch engineManager.state {
                    case .stopped:
                        // 状态1: 未启动 - 只显示开启按钮
                        Button(action: {
                            Task {
                                await engineManager.startEngine()
                                await engineManager.analyzePosition(chessGameViewModel.board.position)
                            }
                        }) {
                            HStack {
                                Image(systemName: "play.circle.fill")
                                Text("Start")
                                    .font(.caption2)
                            }
                        }
                        .help("Start engine and begin analysis")
                        
                    case .paused:
                        // 状态2: 暂停 - 显示恢复和清空按钮
                        But<PERSON>(action: {
                            Task {
                                await engineManager.resumeAnalysis(with: chessGameViewModel.board.position)
                            }
                        }) {
                            HStack {
                                Image(systemName: "play.circle.fill")
                                Text("Resume")
                                    .font(.caption2)
                            }
                        }
                        .help("Resume analysis")
                        
                        Button(action: {
                            Task {
                                await engineManager.clearAnalysisResults()
                            }
                        }) {
                            HStack {
                                Image(systemName: "trash.circle.fill")
                                Text("Clear")
                                    .font(.caption2)
                            }
                        }
                        .help("Clear analysis results and stop engine")
                        
                    case .idle, .analyzing:
                        // 状态3: 运行中 - 显示暂停和清空按钮
                        Button(action: {
                            Task {
                                if engineManager.state == .analyzing {
                                    await engineManager.pauseAnalysis()
                                } else {
                                    await engineManager.analyzePosition(chessGameViewModel.board.position)
                                }
                            }
                        }) {
                            HStack {
                                Image(systemName: engineManager.state == .analyzing ? "pause.circle.fill" : "play.circle.fill")
                                Text(engineManager.state == .analyzing ? "Pause" : "Analyze")
                                    .font(.caption2)
                            }
                        }
                        .help(engineManager.state == .analyzing ? "Pause analysis but keep results" : "Start analyzing current position")
                        
                        Button(action: {
                            Task {
                                await engineManager.clearAnalysisResults()
                            }
                        }) {
                            HStack {
                                Image(systemName: "trash.circle.fill")
                                Text("Clear")
                                    .font(.caption2)
                            }
                        }
                        .help("Clear analysis results and stop engine")
                    }
                    
                    // 设置按钮始终显示
                    Button(action: { 
                        showingEngineSettings = true
                    }) {
                        Image(systemName: "gearshape.fill")
                    }
                    .help("Engine settings")
                }
                .buttonStyle(BorderlessButtonStyle())
                .padding(.trailing, 12)
            }
            .padding(.horizontal, 16)
            .padding(.top, 12) // Added top padding here
            
            // Analysis content
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    switch engineManager.state {
                    case .stopped:
                        // 状态1: 引擎未启动
                        VStack(spacing: 16) {
                            Image(systemName: "cpu")
                                .font(.system(size: 40))
                                .foregroundColor(.secondary)
                            
                            Text("Engine analysis not started")
                                .font(.headline)
                                .foregroundColor(.secondary)
                            
                            Text("Click the Start button to launch the engine and analyze the current position")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 40)
                        
                    case .paused:
                        // 状态2: 引擎暂停 - 显示保存的分析结果
                        VStack(alignment: .leading, spacing: 12) {
                            // 显示保存的分析结果 - 仅显示评估和变化，不显示状态指示器
                            if let eval = engineManager.currentEvaluation {
                                VStack(alignment: .leading, spacing: 8) {
                                    // Analysis statistics with engine name
                                    HStack {
                                        HStack(spacing: 8) {
                                            if let engineName = engineManager.engineInfo?.name {
                                                Text(engineName)
                                                    .font(.caption2)
                                                    .foregroundColor(.blue)
                                                    .fontWeight(.medium)
                                            }

                                            Text("Depth: \(eval.depth)")
                                                .font(.caption2)
                                                .foregroundColor(.secondary)
                                        }

                                        Spacer()

                                        Text(currentEvaluationValue)
                                            .font(.system(.body, design: .monospaced))
                                            .fontWeight(.semibold)
                                            .foregroundColor(evaluationUiColor)
                                    }
                                    .padding(.horizontal, 16)
                                }
                                
                                Divider()
                            }
                            
                            // 显示保存的变化 - 移除候选变化标题
                            if !engineManager.engineLines.isEmpty {
                                VStack(alignment: .leading, spacing: 8) {
                                    ForEach(engineManager.engineLines) { line in
                                        EngineLineView(line: line, chessGameViewModel: chessGameViewModel)
                                    }
                                }
                            } else {
                                VStack(spacing: 8) {
                                    Text("No analysis results")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                            }
                        }
                        
                    case .analyzing:
                        // 状态3: 引擎正在分析
                        VStack(alignment: .leading, spacing: 12) {
                            // Current position evaluation (if available) - 移除分析进行中指示器
                            if let eval = engineManager.currentEvaluation {
                                VStack(alignment: .leading, spacing: 8) {
                                    // Analysis statistics with engine name
                                    HStack {
                                        HStack(spacing: 8) {
                                            if let engineName = engineManager.engineInfo?.name {
                                                Text(engineName)
                                                    .font(.caption2)
                                                    .foregroundColor(.blue)
                                                    .fontWeight(.medium)
                                            }

                                            Text("Depth: \(eval.depth)")
                                                .font(.caption2)
                                                .foregroundColor(.secondary)
                                        }

                                        Spacer()

                                        Text(currentEvaluationValue)
                                            .font(.system(.body, design: .monospaced))
                                            .fontWeight(.semibold)
                                            .foregroundColor(evaluationUiColor)
                                    }
                                    .padding(.horizontal, 16)
                                }
                                
                                Divider()
                            }
                            
                            // Best moves/variations display - 移除候选变化标题
                            if !engineManager.engineLines.isEmpty {
                                VStack(alignment: .leading, spacing: 8) {
                                    ForEach(engineManager.engineLines) { line in
                                        EngineLineView(line: line, chessGameViewModel: chessGameViewModel)
                                    }
                                }
                            } else {
                                // Debug: Show when engine lines are empty
                                VStack(spacing: 8) {
                                    Text("🔍 Waiting for engine analysis results...")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                    
                                    Text("Engine is processing position...")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                            }
                        }
                    case .idle:
                        // 引擎运行但未分析 - 直接显示等待分析结果的界面，跳过中间状态
                        VStack(alignment: .leading, spacing: 12) {
                            VStack(spacing: 8) {
                                Text("🔍 Waiting for engine analysis results...")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                                
                                Text("Engine is processing position...")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                        }
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .sheet(isPresented: $showingEngineSettings) {
            EngineSettingsView(engineManager: engineManager, chessGameViewModel: chessGameViewModel)
        }
    }
    
    // MARK: - Private Properties
    
    
    
    private var currentEvaluationValue: String {
        engineManager.currentEvaluation?.value ?? "--"
    }
    
    private var evaluationUiColor: Color {
        guard let evaluation = engineManager.currentEvaluation?.value else {
            return .primary
        }
        
        if evaluation.hasPrefix("M") {
            return .green // Mate
        } else if evaluation.hasPrefix("+") {
            return .blue // Advantage for white
        } else if evaluation.hasPrefix("-") {
            return .red // Advantage for black
        } else {
            return .primary
        }
    }
}

/// A view that displays a single engine analysis line
struct EngineLineView: View {
    let line: EngineLine
    let chessGameViewModel: ChessGameViewModel
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            // Evaluation score at the beginning
            Text(line.evaluation)
                .font(.system(size: 12, design: .monospaced))
                .fontWeight(.medium)
                .foregroundColor(evaluationColor(for: line.evaluation))
                .frame(minWidth: 50, alignment: .leading)
            
            // Principal variation immediately following the score
            if !line.principalVariation.isEmpty {
                Text(formatPrincipalVariation(line.principalVariation))
                    .font(.system(size: 12, design: .monospaced))
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(line.multiPV == 1 ? Color.blue.opacity(0.05) : Color.gray.opacity(0.02))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 6)
                .stroke(line.multiPV == 1 ? Color.blue.opacity(0.2) : Color.clear, lineWidth: 1)
        )
        .padding(.horizontal, 16)
    }
    
    private func evaluationColor(for evaluation: String) -> Color {
        if evaluation.hasPrefix("M") {
            return .green // Mate
        } else if evaluation.hasPrefix("+") {
            return .blue // Advantage for white
        } else if evaluation.hasPrefix("-") {
            return .red // Advantage for black
        } else {
            return .primary
        }
    }
    
    private func formatPrincipalVariation(_ pv: String) -> String {
        // Split UCI moves
        let uciMoves = pv.components(separatedBy: " ").filter { !$0.isEmpty }
        guard !uciMoves.isEmpty else { return "" }
        
        // Create a board with the analysis position to track moves
        var board = Board(position: line.analysisPosition)
        var formatted = ""
        var isFirstMove = true
        
        for (_, uciMove) in uciMoves.enumerated() {
            // Parse UCI move to Move object
            if let move = EngineLANParser.parse(metaMove: uciMove, for: board.position.sideToMove, in: board.position) {
                // Convert to SAN notation
                let sanMove = move.san
                
                if board.position.sideToMove == .white {
                    // White move - add move number
                    let moveNumber = board.position.clock.fullmoves
                    formatted += "\(moveNumber). \(sanMove) "
                } else {
                    // Black move
                    let moveNumber = board.position.clock.fullmoves
                    if isFirstMove {
                        // First move is black - use "1…e5" format
                        formatted += "\(moveNumber)…\(sanMove) "
                    } else {
                        // Subsequent black moves - just the move
                        formatted += "\(sanMove) "
                    }
                }
                
                isFirstMove = false
                
                // Make the move on the board to update position for next iteration
                board.move(pieceAt: move.start, to: move.end)
            } else {
                // Fallback to UCI notation if parsing fails
                if board.position.sideToMove == .white {
                    let moveNumber = board.position.clock.fullmoves
                    formatted += "\(moveNumber). \(uciMove) "
                } else {
                    let moveNumber = board.position.clock.fullmoves
                    if isFirstMove {
                        // First move is black - use "1…e2e4" format (UCI fallback)
                        formatted += "\(moveNumber)…\(uciMove) "
                    } else {
                        formatted += "\(uciMove) "
                    }
                }
                
                isFirstMove = false
                
                // Try to parse squares manually for position updates
                if uciMove.count >= 4 {
                    let startSquareStr = String(uciMove.prefix(2))
                    let endSquareStr = String(uciMove.dropFirst(2).prefix(2))
                    
                    let startSquare = Square(startSquareStr)
                    let endSquare = Square(endSquareStr)
                    board.move(pieceAt: startSquare, to: endSquare)
                }
            }
        }
        
        return formatted.trimmingCharacters(in: .whitespaces)
    }
}

/// A simplified engine analysis content view without header controls
struct EngineAnalysisContentView: View {
    @ObservedObject var engineManager: EngineManager // Changed
    let chessGameViewModel: ChessGameViewModel // Added

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            switch engineManager.state {
            case .stopped:
                // Placeholder content when engine is not running
                VStack(spacing: 16) {
                    Image(systemName: "cpu")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                    
                    Text("Engine analysis not started")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Start analysis from the main engine panel")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            case .analyzing:
                // Engine is analyzing - show progress
                VStack(alignment: .leading, spacing: 12) {
                    // Analysis status indicator
                    HStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                            .scaleEffect(0.8)
                        
                        Text("Analyzing...")
                            .font(.subheadline)
                            .foregroundColor(.blue)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    
                    // Current position evaluation (if available)
                    if engineManager.currentEvaluation != nil { // Changed
                        HStack {
                            HStack(spacing: 8) {
                                if let engineName = engineManager.engineInfo?.name {
                                    Text(engineName)
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                        .fontWeight(.medium)
                                }

                                Text("Evaluation:")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                            Text(currentEvaluationValue) // Changed
                                .font(.system(.body, design: .monospaced))
                                .fontWeight(.semibold)
                                .foregroundColor(evaluationUiColor) // Changed
                        }
                        .padding(.horizontal, 16)

                        Divider()
                    }
                    
                    // Best moves/variations
                    if !engineManager.engineLines.isEmpty { // Changed
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Best Moves:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 16)
                            
                            ForEach(engineManager.engineLines) { line in // Changed
                                EngineLineView(line: line, chessGameViewModel: chessGameViewModel)
                            }
                        }
                    } else {
                        // Waiting for results
                        VStack(spacing: 8) {
                            Text("🔍 Waiting for analysis results...")
                                .font(.caption)
                                .foregroundColor(.orange)
                            
                            Text("Engine is processing...")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                    }
                }
            case .idle, .paused:
                // Engine is running but not analyzing - show same waiting state as when analyzing
                VStack(spacing: 8) {
                    Text("🔍 Waiting for analysis results...")
                        .font(.caption)
                        .foregroundColor(.orange)
                    
                    Text("Engine is processing...")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            }
        }
    }
    
    // MARK: - Private Properties
    
    

    private var currentEvaluationValue: String { // Changed name and implementation
        engineManager.currentEvaluation?.value ?? "--"
    }
    
    private var evaluationUiColor: Color { // Changed name and implementation
        guard let evaluation = engineManager.currentEvaluation?.value else {
            return .primary
        }
        
        if evaluation.hasPrefix("M") {
            return .green // Mate
        } else if evaluation.hasPrefix("+") {
            return .blue // Advantage for white
        } else if evaluation.hasPrefix("-") {
            return .red // Advantage for black
        } else {
            return .primary
        }
    }
}

#Preview {
    let vm = ChessGameViewModel()
    return EngineAnalysisView(engineManager: vm.engineManager, chessGameViewModel: vm)
        .frame(height: 300)
}

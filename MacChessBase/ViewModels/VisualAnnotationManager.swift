
//
//  VisualAnnotationManager.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/8/3.
//

import SwiftUI
import ChessKit

@MainActor
class VisualAnnotationManager: ObservableObject {
    // MARK: - Session Reference
    private var sessionProvider: (() -> GameSession?)?
    private var session: GameSession? { sessionProvider?() }

    // MARK: - Published Properties
    @Published var currentAnnotationColor: Move.VisualAnnotations.AnnotationColor = .green

    // MARK: - Initialization
    init() {
        // Initialized without a session provider, which will be set later.
    }

    // MARK: - Public Methods

    /// Sets the session provider for the manager.
    /// This is called by the parent ViewModel after initialization to avoid retain cycles.
    func setSessionProvider(_ sessionProvider: @escaping () -> GameSession?) {
        self.sessionProvider = sessionProvider
    }

    /// Sets the annotation color
    func setAnnotationColor(_ color: Move.VisualAnnotations.AnnotationColor) {
        self.currentAnnotationColor = color
        print("Annotation color set to: \(color)")
    }

    /// Toggles a square highlight for the current move position
    func toggleSquareHighlight(at square: Square) {
        guard let session = session else { return }
        
        let currentMoveIndex = session.currentMoveIndex
        print("Attempting to toggle square highlight at \(square) with color \(self.currentAnnotationColor)")
        
        guard var move = session.game.moves.getNodeMove(index: currentMoveIndex) else {
            print("Cannot toggle highlight: No move at current index.")
            return
        }
        
        var squareHighlights = move.positionComment.visualAnnotations.squareHighlights
        let color = self.currentAnnotationColor
        
        if let existingIndex = squareHighlights.firstIndex(where: { $0.square == square }) {
            let existingHighlight = squareHighlights[existingIndex]
            if existingHighlight.color == color {
                squareHighlights.remove(at: existingIndex)
            } else {
                squareHighlights[existingIndex] = .init(color: color, square: square)
            }
        } else {
            squareHighlights.append(.init(color: color, square: square))
        }
        
        move.positionComment.visualAnnotations.squareHighlights = squareHighlights
        _ = session.editMove(at: currentMoveIndex, newMove: move)
    }

    /// Toggles an arrow annotation for the current move position
    func toggleArrow(from: Square, to: Square) {
        guard let session = session else { return }
        
        let currentMoveIndex = session.currentMoveIndex
        print("Attempting to toggle arrow from \(from) to \(to) with color \(self.currentAnnotationColor)")
        
        guard var move = session.game.moves.getNodeMove(index: currentMoveIndex) else {
            print("Cannot toggle arrow: No move at current index.")
            return
        }
        
        var arrows = move.positionComment.visualAnnotations.arrows
        let color = self.currentAnnotationColor
        
        if let existingIndex = arrows.firstIndex(where: { $0.from == from && $0.to == to }) {
            let existingArrow = arrows[existingIndex]
            if existingArrow.color == color {
                arrows.remove(at: existingIndex)
            } else {
                arrows[existingIndex] = .init(color: color, from: from, to: to)
            }
        } else {
            arrows.append(.init(color: color, from: from, to: to))
        }
        
        move.positionComment.visualAnnotations.arrows = arrows
        _ = session.editMove(at: currentMoveIndex, newMove: move)
    }
}

//
//  MoveHandler.swift
//  MacChessBase
//
//  Created by Gemini on 2025/8/4.
//

import Foundation
import ChessKit
import Swift<PERSON>

@MainActor
final class MoveHandler {
    private weak var viewModel: ChessGameViewModel?

    init(viewModel: ChessGameViewModel) {
        self.viewModel = viewModel
    }

    private var session: GameSession? {
        viewModel?.session
    }

    private var board: Board {
        viewModel?.board ?? Board()
    }

    // MARK: - Game Actions

    /// Handles square selection and piece movement
    func handleSquarePress(_ square: Square) {
        guard let viewModel = viewModel else { return }
        if let selectedSquare = viewModel.selectedSquare {
            // Normal mode with a piece already selected
            attemptMove(from: selectedSquare, to: square)
        } else {
            // No selection, try to select a piece
            selectSquare(square)
        }
    }

    /// Selects a square if it contains a piece of the current player
    private func selectSquare(_ square: Square) {
        guard let viewModel = viewModel else { return }
        guard let piece = board.position.piece(at: square),
              piece.color == board.position.sideToMove else {
            viewModel.clearSelection()
            return
        }
        
        viewModel.selectedSquare = square
        viewModel.possibleMoves = board.legalMoves(forPieceAt: square)
    }

    /// Attempts to move a piece from one square to another
    func attemptMove(from startSquare: Square, to endSquare: Square) {
        guard let viewModel = viewModel else { return }

        // If clicking the same square, deselect
        if startSquare == endSquare {
            viewModel.clearAllSelections()
            return
        }
        
        // If clicking another piece of the same color, select it instead
        if let piece = board.position.piece(at: endSquare),
           piece.color == board.position.sideToMove {
            selectSquare(endSquare)
            return
        }
        
        // Check if the move is legal first, but don't execute it yet
        if board.canMove(pieceAt: startSquare, to: endSquare) {
            // Create a temporary move to check for variations
            var tempBoard = board
            if let tempMove = tempBoard.move(pieceAt: startSquare, to: endSquare) {
                // Check if we need to show variation creation dialog before making the actual move
                if shouldShowVariationCreationDialog(for: tempMove) {
                    // Store the move and show dialog, but don't execute the move yet
                    viewModel.pendingMove = tempMove
                    viewModel.pendingMoveFromIndex = viewModel.currentMoveIndex
                    viewModel.existingNextMoveIndex = getExistingNextMoveIndex(for: tempMove)
                    viewModel.showVariationCreationDialog = true
                    viewModel.clearAllSelections()
                    return
                }
                
                // No variation dialog needed, execute the move directly
                if let actualMove = viewModel.board.move(pieceAt: startSquare, to: endSquare) {
                    handleMoveResult(actualMove)
                }
            }
        } else {
            // Invalid move, clear all selections
            viewModel.clearAllSelections()
        }
    }

    /// Handles the result of a successful move (move has already been executed on the board)
    private func handleMoveResult(_ move: Move) {
        guard let viewModel = viewModel else { return }
        viewModel.lastMove = move
        
        guard let metaMove = move.metaMove else {
            return
        }
        
        // Play move sound
        viewModel.soundManager.playMoveSound(for: move)
        
        // Check for pawn promotion
        if metaMove.piece.kind == .pawn &&
           (metaMove.end.rank.value == 8 || metaMove.end.rank.value == 1) &&
            metaMove.promotedPiece == nil {
            viewModel.promotionMove = move
            viewModel.showPromotionDialog = true
            viewModel.clearAllSelections()
            return
        }
        
        // Complete the move sequence - the move has already been executed on the board
        completeExecutedMoveSequence(move)
    }

    /// Completes a move sequence for a move that has already been executed on the board
    private func completeExecutedMoveSequence(_ move: Move) {
        guard let viewModel = viewModel, let session = session else { return }
        
        // Check if there's already a next move that would create a variation
        if let existingNextIndex = session.game.moves.hasNextMove(containing: move, for: viewModel.currentMoveIndex) {
            // Move already exists, just navigate to it
            session.goToMove(at: existingNextIndex)
            viewModel.clearAllSelections()
            return
        }
        
        // No existing moves, proceed with normal move creation in the game tree
        // (the move has already been executed on the board)
        let newIndex = session.makeMoveInGame(move, from: viewModel.currentMoveIndex)
        session.goToMove(at: newIndex)
        
        viewModel.clearAllSelections()
        checkGameStatus(move)
        
        // Throttled cache invalidation to prevent excessive recalculation
        viewModel.throttledCacheInvalidation()
    }

    /// Executes a move with the specified variation creation option
    func executeMove(_ move: Move, option: ChessGameViewModel.VariationCreationOption) {
        guard let viewModel = viewModel, let session = session else { return }
        let fromIndex = viewModel.pendingMoveFromIndex ?? viewModel.currentMoveIndex
        
        guard let metaMove = move.metaMove else {
            return
        }
        
        // First, execute the move on the board
        guard let executedMove = viewModel.board.move(pieceAt: metaMove.start, to: metaMove.end) else {
            print("Failed to execute move on board")
            return
        }
        
        // Update last move and play sound
        viewModel.lastMove = executedMove
        viewModel.soundManager.playMoveSound(for: executedMove)
        
        // Check for pawn promotion
        if executedMove.metaMove!.piece.kind == .pawn &&
            (executedMove.metaMove!.end.rank.value == 8 || executedMove.metaMove!.end.rank.value == 1) &&
            executedMove.metaMove!.promotedPiece == nil {
            viewModel.promotionMove = executedMove
            viewModel.showPromotionDialog = true
            viewModel.clearAllSelections()
            return
        }
        
        // Add the move to the game tree based on the selected option
        switch option {
        case .newVariation:
            // Default behavior: create new variation
            let newIndex = session.makeMoveInGame(executedMove, from: fromIndex)
            session.goToMove(at: newIndex)
            
        case .newMainLine:
            // Create new variation and promote it
            let newMoveIndex = session.makeMoveInGame(executedMove, from: fromIndex)
            _ = session.promoteVariation(at: newMoveIndex)
            session.goToMove(at: newMoveIndex)
            
        case .overwrite:
            // Delete all moves after the current position (both next and children)
            _ = session.overwriteMove(executedMove, from: fromIndex)
            // goToMove is called within overwriteMove
        }
        
        viewModel.clearAllSelections()
        checkGameStatus(executedMove)
        
        // Clear pending move data
        viewModel.pendingMove = nil
        viewModel.pendingMoveFromIndex = nil
        viewModel.existingNextMoveIndex = nil
        
        // Throttled cache invalidation to prevent excessive recalculation
        viewModel.throttledCacheInvalidation()
    }

    /// Completes a pawn promotion
    func completePromotion(to pieceKind: Piece.Kind) {
        guard let viewModel = viewModel, let promotionMove = viewModel.promotionMove else { return }
        
        let completedMove = viewModel.board.completePromotion(of: promotionMove, to: pieceKind)
        
        // Since promotion moves are always created as part of executeMove, 
        // we need to complete the move sequence
        completeExecutedMoveSequence(completedMove)
        
        viewModel.promotionMove = nil
        viewModel.showPromotionDialog = false
    }

    // MARK: - Drag and Drop Validation
    
    /// Validates if a drag operation can start for the given piece and square
    func validateDragStart(piece: Piece, from square: Square) -> Bool {
        guard let viewModel = viewModel else { return false }
        guard piece.color == viewModel.board.position.sideToMove else { return false }
        return true
    }
    
    /// Checks if a piece at the given square can be moved (has legal moves)
    func canMovePiece(at square: Square) -> Bool {
        guard let viewModel = viewModel else { return false }
        guard let piece = viewModel.board.position.piece(at: square),
              piece.color == viewModel.board.position.sideToMove else { return false }
        return !viewModel.board.legalMoves(forPieceAt: square).isEmpty
    }
    
    /// Sets the selected square for move validation
    func setSelectedSquare(_ square: Square) {
        guard let viewModel = viewModel else { return }
        viewModel.selectedSquare = square
        viewModel.possibleMoves = viewModel.board.legalMoves(forPieceAt: square)
    }

    /// Checks the game status after a move
    func checkGameStatus(_ move: Move) {
        guard let viewModel = viewModel else { return }
        guard let metaMove = move.metaMove else {
            viewModel.gameStatus = .inProgress
            return
        }
        switch metaMove.checkState {
        case .checkmate:
            viewModel.gameStatus = .checkmate(board.position.sideToMove.opposite)
        case .stalemate:
            viewModel.gameStatus = .stalemate
        default:
            // Check for other draw conditions
            if board.position.clock.halfmoves >= 100 {
                viewModel.gameStatus = .draw(.fiftyMoves)
            } else if board.position.hasInsufficientMaterial {
                viewModel.gameStatus = .draw(.insufficientMaterial)
            } else {
                viewModel.gameStatus = .inProgress
            }
        }
    }

    // MARK: - Variation Creation

    /// Handles variation creation option selection
    func selectVariationCreationOption(_ option: ChessGameViewModel.VariationCreationOption) {
        guard let viewModel = viewModel, let move = viewModel.pendingMove else { return }
        
        viewModel.showVariationCreationDialog = false
        viewModel.isKeyboardNavigationDisabled = false  // Re-enable navigation
        
        // Add a small delay to ensure UI is fully reset after sheet dismissal
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.executeMove(move, option: option)
        }
    }

    /// Cancels variation creation dialog
    func cancelVariationCreation() {
        guard let viewModel = viewModel else { return }
        viewModel.showVariationCreationDialog = false
        viewModel.isKeyboardNavigationDisabled = false  // Re-enable navigation
        viewModel.pendingMove = nil
        viewModel.pendingMoveFromIndex = nil
        viewModel.existingNextMoveIndex = nil
        
        // Restore the board to the current move index position
        // This ensures the board shows the correct state after cancellation
        viewModel.onGameStateChanged()
    }

    /// Checks if a variation creation dialog should be shown for the given move
    private func shouldShowVariationCreationDialog(for move: Move) -> Bool {
        guard let viewModel = viewModel, let session = session else { return false }
        
        // Check if there's already a next move that would create a variation
        if session.game.moves.hasNextMove(containing: move, for: viewModel.currentMoveIndex) != nil {
            // Move already exists, no dialog needed
            return false
        }
        
        // Special handling for the first move (from starting position)
        if viewModel.currentMoveIndex == session.game.startingIndex {
            // Check if there's already a first move in the game
            if !session.game.moves.isEmpty {
                // There's already a first move, this will create a variation
                return true
            }
        }
        
        // Check if there are any existing moves from this position (main line or variations)
        let mainLineNextIndex = session.game.moves.nextIndex(currentIndex: viewModel.currentMoveIndex)
        let variations = session.game.moves.variations(from: viewModel.currentMoveIndex)
        
        // If there's a main line move OR variations, this new move will create a variation
        return mainLineNextIndex != nil || !variations.isEmpty
    }

    /// Gets the existing next move index for the current position
    private func getExistingNextMoveIndex(for move: Move) -> MoveTree.MoveIndex? {
        guard let viewModel = viewModel, let session = session else { return nil }
        
        if viewModel.currentMoveIndex == session.game.startingIndex {
            return session.game.moves.nextIndex(currentIndex: viewModel.currentMoveIndex)
        }
        return session.game.moves.nextIndex(currentIndex: viewModel.currentMoveIndex)
    }

    // MARK: - Reverse Drag

    /// Finds all squares containing pieces that can move to the target square
    private func findSourceSquares(for targetSquare: Square) -> [Square] {
        guard let viewModel = viewModel else { return [] }
        var sourceSquares: [Square] = []
        
        // Check all squares on the board
        for rank in 1...8 {
            for file in ["a", "b", "c", "d", "e", "f", "g", "h"] {
                let square = Square("\(file)\(rank)")
                let piece = board.position.piece(at: square)
                
                // Only check pieces of the current player
                guard let piece = piece, piece.color == board.position.sideToMove else { continue }
                
                // Check if this piece can move to the target square
                let legalMoves = board.legalMoves(forPieceAt: square)
                if legalMoves.contains(targetSquare) {
                    sourceSquares.append(square)
                }
            }
        }
        
        print("findSourceSquares for \(targetSquare): found \(sourceSquares.count) pieces - \(sourceSquares)")
        return sourceSquares
    }
    
    /// Starts a reverse drag from a target square
    func startReverseDrag(from targetSquare: Square) -> Bool {
        guard let viewModel = viewModel else { return false }
        let piece = board.position.piece(at: targetSquare)
        
        print("startReverseDrag from \(targetSquare), piece: \(piece?.description ?? "empty"), sideToMove: \(board.position.sideToMove)")
        
        // Only allow reverse drag from empty squares or opponent pieces
        if piece == nil || piece?.color != board.position.sideToMove {
            let validSources = findSourceSquares(for: targetSquare)
            
            // Enter reverse drag mode
            viewModel.isReverseDragActive = true
            viewModel.reverseDragTarget = targetSquare
            viewModel.reverseDragValidSources = validSources
            viewModel.clearSelection() // Clear normal selection
            
            print("startReverseDrag success, found \(validSources.count) source pieces: \(validSources)")
            return true
        }
        print("startReverseDrag failed - trying to drag own piece")
        return false
    }
    
    /// Completes a reverse drag to a source square
    func completeReverseDrag(to sourceSquare: Square) {
        guard let viewModel = viewModel else { return }
        guard viewModel.isReverseDragActive,
              let targetSquare = viewModel.reverseDragTarget else {
            cancelReverseDrag()
            return
        }
        
        print("completeReverseDrag from \(sourceSquare) to \(targetSquare)")
        
        // Check if the source square is valid for this reverse drag
        if viewModel.reverseDragValidSources.contains(sourceSquare) {
            print("Valid reverse drag move, attempting move from \(sourceSquare) to \(targetSquare)")
            attemptMove(from: sourceSquare, to: targetSquare)
        } else {
            print("Invalid reverse drag move - source \(sourceSquare) not in valid sources: \(viewModel.reverseDragValidSources)")
        }
        
        cancelReverseDrag()
    }
    
    /// Cancels the reverse drag operation
    func cancelReverseDrag() {
        guard let viewModel = viewModel else { return }
        viewModel.isReverseDragActive = false
        viewModel.reverseDragTarget = nil
        viewModel.reverseDragValidSources = []
    }
}

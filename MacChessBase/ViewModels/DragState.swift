//
//  DragState.swift
//  MacChessBase
//
//  Created by <PERSON> on 2025/6/13.
//

import SwiftUI
import ChessKit
import AppKit

/// Independent drag state management for chess board interactions
/// This prevents drag operations from triggering unnecessary UI updates in other components
@MainActor
final class DragState: ObservableObject {
    @Published var draggedPiece: (piece: Piece, fromSquare: Square)?
    @Published var dragPosition: CGPoint = .zero
    
    // Reverse drag state
    @Published var reverseDraggedPiece: (piece: Piece, fromSquare: Square)?
    @Published var reverseDragPosition: CGPoint = .zero
    
    /// Starts dragging a piece
    func startDrag(piece: Piece, from square: Square, at position: CGPoint) {
        draggedPiece = (piece: piece, fromSquare: square)
        dragPosition = position
    }
    
    /// Updates the drag position during drag operation
    func updateDrag(to position: CGPoint) {
        dragPosition = position
    }
     /// Ends the drag operation and clears drag state
    func endDrag() {
        draggedPiece = nil
        dragPosition = .zero
    }
    
    /// Starts reverse dragging a piece (opponent piece being dragged)
    func startReverseDrag(piece: Piece, from square: Square, at position: CGPoint) {
        reverseDraggedPiece = (piece: piece, fromSquare: square)
        reverseDragPosition = position
    }
    
    /// Updates the reverse drag position during reverse drag operation
    func updateReverseDrag(to position: CGPoint) {
        reverseDragPosition = position
    }
    
    /// Ends the reverse drag operation and clears reverse drag state
    func endReverseDrag() {
        reverseDraggedPiece = nil
        reverseDragPosition = .zero
    }

    /// Cancels the drag operation
    func cancelDrag() {
        draggedPiece = nil
        dragPosition = .zero
        reverseDraggedPiece = nil
        reverseDragPosition = .zero
        NSCursor.arrow.set()
    }

    /// Returns true if currently dragging from the specified square
    func isDraggedFrom(_ square: Square) -> Bool {
        return draggedPiece?.fromSquare == square
    }
    
    /// Returns true if currently reverse dragging from the specified square
    func isReverseDraggedFrom(_ square: Square) -> Bool {
        return reverseDraggedPiece?.fromSquare == square
    }
}

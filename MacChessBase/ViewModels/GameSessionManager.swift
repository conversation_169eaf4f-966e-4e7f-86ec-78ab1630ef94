//
//  GameSessionManager.swift
//  MacChessBase
//
//  Created on 2025/6/8.
//

import SwiftUI
import ChessKit
import Foundation

/// Manages multiple chess game sessions
@MainActor
class GameSessionManager: ObservableObject {
    @Published var sessions: [GameSession] = []
    @Published var activeSession: GameSession?
    var newGameCount = 0
    private let fileManager = ChessFileManager.shared
    
    init() {
        // Create initial session
        createNewSession()
    }
    
    /// Creates a new game session
    @discardableResult
    func createNewSession(name: String = "") -> GameSession {
        let session = GameSession(name: name.isEmpty ? generateDefaultName() : name)
        sessions.append(session)
        setActiveSession(session)
        return session
    }
    
    /// Sets the active session
    func setActiveSession(_ session: GameSession) {
        // Check if the session exists in our sessions list
        guard sessions.contains(where: { $0.id == session.id }) else {
            // Session doesn't exist in our managed sessions, ignore the request
            print("Warning: Attempting to set an unmanaged session as active. Session ID: \(session.id)")
            return
        }
        
        // Mark all sessions as inactive
        for s in sessions {
            s.isActive = false
        }
        
        // Mark the selected session as active
        session.isActive = true
        activeSession = session
    }
    
    /// Removes a session
    func removeSession(_ session: GameSession) {
        guard sessions.count > 1 else {
            // Don't remove the last session, just reset it
            session.newGame()
            return
        }
        newGameCount -= 1
        
        if let index = sessions.firstIndex(where: { $0.id == session.id }) {
            sessions.remove(at: index)
            
            // If the removed session was active, select another one
            if session.isActive {
                if !sessions.isEmpty {
                    setActiveSession(sessions.first!)
                }
            }
        }
    }
    
    /// Gets the current active view model for the chess board
    var currentViewModel: ChessGameViewModel? {
        return activeSession?.viewModel
    }
    
    /// Generates a default name for new sessions
    private func generateDefaultName() -> String {
        newGameCount += 1
        if newGameCount == 1 {
            return "New Game"
        }
        return "New Game \(newGameCount)"
    }
    
    /// Sorts sessions by creation order (first created at top, new ones at bottom)
    var sortedSessions: [GameSession] {
        return sessions
    }
    
    /// Creates a new session and starts a new game
    func createNewGame() {
        let session = createNewSession()
        session.newGame()
    }

    /// Creates a new session at a specific position and starts a new game
    func createNewGame(at index: Int) {
        let session = GameSession(name: generateDefaultName())
        session.newGame()

        // Insert at the specified index, clamped to valid range
        let insertIndex = max(0, min(index, sessions.count))
        sessions.insert(session, at: insertIndex)
        setActiveSession(session)
    }

    /// Creates a new session before the specified session
    func createNewGameBefore(_ targetSession: GameSession) {
        guard let index = sessions.firstIndex(where: { $0.id == targetSession.id }) else {
            // If target session not found, create at the end
            createNewGame()
            return
        }
        createNewGame(at: index)
    }

    /// Creates a new session after the specified session
    func createNewGameAfter(_ targetSession: GameSession) {
        guard let index = sessions.firstIndex(where: { $0.id == targetSession.id }) else {
            // If target session not found, create at the end
            createNewGame()
            return
        }
        createNewGame(at: index + 1)
    }

    /// Removes all sessions except one and resets it
    func removeAllSessions() {
        // Keep only the first session and reset it
        if let firstSession = sessions.first {
            // Remove all other sessions
            sessions = [firstSession]

            // Reset the remaining session
            firstSession.newGame()
            setActiveSession(firstSession)

            // Reset the game counter
            newGameCount = 1
            firstSession.name = "New Game"
        } else {
            // If no sessions exist, create a new one
            sessions.removeAll()
            newGameCount = 0
            createNewSession()
        }
    }
    
    /// Loads a game from file in a new session (updated to support multi-game PGN)
    func loadGameInNewSession(from url: URL) {
        do {
            let content = try fileManager.readPGNContent(from: url)
            let fileExtension = url.pathExtension.lowercased()
            
            if fileExtension == "fen" {
                // FEN files are always single games
                let game = try fileManager.readGame(from: url)
                let session = createNewSession()
                session.importGameWithoutUndo(game)
                
                // Try to extract a name from the file
                let fileName = url.deletingPathExtension().lastPathComponent
                if !fileName.isEmpty && fileName != "Untitled" {
                    session.name = fileName
                }
            } else {
                // PGN files may contain multiple games - use new import logic
                importPGN(content)
            }
        } catch {
            print("Error loading game from file: \(error)")
            // Create empty session as fallback
            createNewSession()
        }
    }
    
    /// Import PGN with multi-game detection and routing
    func importPGN(_ pgn: String) {
        // Use ChessKit's parseMultiple to detect if there are multiple games
        let games = PGNParser.parseMultiple(games: pgn)
        
        if games.isEmpty {
            print("No valid games found in PGN")
            return
        }
        
        if games.count == 1 {
            // Single game - use existing logic with undo/redo support
            if let activeSession = activeSession {
                if let undoState = activeSession.loadGameFromObjectWithUndo(games[0]) {
                    // Register undo operation
                    activeSession.undoManager?.registerUndo(withTarget: activeSession) { target in
                        MainActor.assumeIsolated {
                            _ = target.undoImportGame(undoState)
                            
                            // Register redo operation
                            target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                                MainActor.assumeIsolated {
                                    redoTarget.importGameWithoutUndo(games[0])
                                }
                            }
                        }
                    }
                    activeSession.undoManager?.setActionName("Import PGN")
                }
            } else {
                // Create new session if no active session
                let session = createNewSession()
                session.importGameWithoutUndo(games[0])
                extractGameName(for: session, from: games[0])
            }
        } else {
            // Multiple games - create separate sessions without undo/redo
            for (index, game) in games.enumerated() {
                let session = createNewSession()
                session.importGameWithoutUndo(game)
                
                // Extract name from game metadata or use index
                extractGameName(for: session, from: game, gameIndex: index + 1)
            }
            
            print("Imported \(games.count) games into separate sessions")
        }
    }
    
    /// Helper method to extract and set game name from Game object
    private func extractGameName(for session: GameSession, from game: Game, gameIndex: Int? = nil) {
        let tags = game.tags
        
        if !tags.event.isEmpty {
            let baseName = tags.event
            session.name = gameIndex != nil ? "\(baseName) #\(gameIndex!)" : baseName
        } else if !tags.white.isEmpty || !tags.black.isEmpty {
            let white = tags.white.isEmpty ? "?" : tags.white
            let black = tags.black.isEmpty ? "?" : tags.black
            let baseName = "\(white) - \(black)"
            session.name = gameIndex != nil ? "\(baseName) #\(gameIndex!)" : baseName
        } else if let gameIndex = gameIndex {
            session.name = "Game #\(gameIndex)"
        }
    }
    
    /// Import from clipboard with multi-game detection
    func importFromClipboard() -> Bool {
        guard let content = fileManager.readPGNFromClipboard() else {
            return false
        }
        
        importPGN(content)
        return true
    }
}

//
//  SoundManagerTests.swift
//  MacChessBaseTests
//
//  Created by <PERSON> on 2025/7/9.
//

import XCTest
import AVFoundation
@testable import MacChessBase
@testable import ChessKit

@MainActor
final class SoundManagerTests: XCTestCase {
    
    var soundManager: SoundManager!
    
    override func setUp() {
        super.setUp()
        soundManager = SoundManager()
        
        // Give the sound manager time to initialize
        let expectation = XCTestExpectation(description: "Sound manager initialization")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
    }
    
    override func tearDown() {
        soundManager = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    func testSoundManagerInitialization() {
        // Test that sound manager initializes without crashing
        XCTAssertNotNil(soundManager)
        
        // Test creating multiple instances doesn't crash
        let anotherSoundManager = SoundManager()
        XCTAssertNotNil(anotherSoundManager)
    }
    
    // MARK: - Sound Type Tests
    
    func testSoundTypeProperties() {
        // Test move sound properties
        XCTAssertEqual(SoundManager.SoundType.move.rawValue, "move")
        XCTAssertEqual(SoundManager.SoundType.move.fileName, "move.mp3")
        XCTAssertEqual(SoundManager.SoundType.move.volume, 0.7)
        
        // Test capture sound properties
        XCTAssertEqual(SoundManager.SoundType.capture.rawValue, "capture")
        XCTAssertEqual(SoundManager.SoundType.capture.fileName, "capture.mp3")
        XCTAssertEqual(SoundManager.SoundType.capture.volume, 0.8)
    }
    
    func testSoundTypeAllCases() {
        let allTypes = SoundManager.SoundType.allCases
        XCTAssertEqual(allTypes.count, 2)
        XCTAssertTrue(allTypes.contains(.move))
        XCTAssertTrue(allTypes.contains(.capture))
    }
    
    func testVolumeRanges() {
        // Test that volumes are within valid range [0.0, 1.0]
        for soundType in SoundManager.SoundType.allCases {
            XCTAssertGreaterThanOrEqual(soundType.volume, 0.0)
            XCTAssertLessThanOrEqual(soundType.volume, 1.0)
        }
        
        // Test that capture is louder than move
        XCTAssertGreaterThan(SoundManager.SoundType.capture.volume, SoundManager.SoundType.move.volume)
    }
    
    // MARK: - Move Sound Detection Tests
    
    func testPlayMoveSound() {
        // Test that playing move sound doesn't crash
        // Create a simple regular move
        let from = Square.e2
        let to = Square.e4
        let piece = Piece(.pawn, color: .white, square: .a1)
        
        let metaMove = MetaMove(
            result: .move,
            piece: piece,
            start: from,
            end: to
        )
        
        let move = Move(metaMove: metaMove)
        
        // This should not crash and should play the move sound
        XCTAssertNoThrow(soundManager.playMoveSound(for: move))
    }
    
    func testPlayCaptureSound() {
        // Test capture sound detection
        let from = Square.e5
        let to = Square.d6
        let piece = Piece(.pawn, color: .white, square: .a1)
        let capturedPiece = Piece(.pawn, color: .black, square: .a1)
        
        let metaMove = MetaMove(
            result: .capture(capturedPiece),
            piece: piece,
            start: from,
            end: to
        )
        
        let move = Move(metaMove: metaMove)
        
        // This should not crash and should play the capture sound
        XCTAssertNoThrow(soundManager.playMoveSound(for: move))
    }
    
    func testPlaySoundForInvalidMove() {
        // Test with a move that has no metaMove
        let move = Move()
        
        // This should not crash, just do nothing
        XCTAssertNoThrow(soundManager.playMoveSound(for: move))
    }
    
    // MARK: - MetaMove.Result Extension Tests
    
    func testMoveResultIsCaptureExtension() {
        // Test regular move
        let regularMove = MetaMove.Result.move
        XCTAssertFalse(regularMove.isCapture)
        
        // Test capture
        let capturedPiece = Piece(.pawn, color: .black, square: .a1)
        let captureMove = MetaMove.Result.capture(capturedPiece)
        XCTAssertTrue(captureMove.isCapture)
        
        // Test castling moves
        let castlingMove = MetaMove(result: .castle(.wK), piece: Piece(.king, color: .white, square: .a1), start: .e1, end: .g1)
        XCTAssertEqual(castlingMove.result, .castle(.wK))
        XCTAssertNotEqual(castlingMove.result, .castle(.wQ))
    }
    
    // MARK: - Sound File Loading Tests
    
    func testSoundFileExistence() {
        // Note: These tests check for sound file existence in the bundle
        // In a real app, the sound files should exist
        
        for soundType in SoundManager.SoundType.allCases {
            let fileName = soundType.rawValue
            let fileURL = Bundle.main.url(forResource: fileName, withExtension: "mp3")
            
            // In test environment, files might not exist - this tests the behavior
            if fileURL == nil {
                print("Warning: Sound file \(soundType.fileName) not found in bundle - this is expected in test environment")
            }
        }
    }
    
    func testSoundManagerHandlesMissingFiles() {
        // Test that sound manager handles missing sound files gracefully
        // This should not crash even if sound files are missing
        
        let from = Square.e2
        let to = Square.e4
        let piece = Piece(.pawn, color: .white, square: .a1)
        
        let metaMove = MetaMove(
            result: .move,
            piece: piece,
            start: from,
            end: to
        )
        
        let move = Move(metaMove: metaMove)
        
        XCTAssertNoThrow(soundManager.playMoveSound(for: move))
    }
    
    // MARK: - Complex Move Type Tests
    
    func testCastlingMoves() {
        // Test kingside castling
        let kingsideCastling = MetaMove(
            result: .castle(.wK),
            piece: Piece(.king, color: .white, square: .a1),
            start: Square.e1,
            end: Square.g1
        )
        
        let kingsideCastlingMove = Move(metaMove: kingsideCastling)
        XCTAssertNoThrow(soundManager.playMoveSound(for: kingsideCastlingMove))
        
        // Test queenside castling
        let queensideCastling = MetaMove(
            result: .castle(.wQ),
            piece: Piece(.king, color: .white, square: .a1),
            start: Square.e1,
            end: Square.c1
        )
        
        let queensideCastlingMove = Move(metaMove: queensideCastling)
        XCTAssertNoThrow(soundManager.playMoveSound(for: queensideCastlingMove))
    }
    
    func testEnPassantCapture() {
        let capturedPawn = Piece(.pawn, color: .black, square: .a1)
        let enPassant = MetaMove(
            result: .capture(capturedPawn),
            piece: Piece(.pawn, color: .white, square: .a1),
            start: Square.e5,
            end: Square.d6
        )
        
        let enPassantMove = Move(metaMove: enPassant)
        XCTAssertNoThrow(soundManager.playMoveSound(for: enPassantMove))
        
        // En passant should be treated as a capture
        XCTAssertTrue(enPassant.result.isCapture)
    }
    
    func testPromotionMoves() {
        let promotion = MetaMove(
            result: .move,
            piece: Piece(.pawn, color: .white, square: .a1),
            start: Square.e7,
            end: Square.e8
        )
        
        let promotionMove = Move(metaMove: promotion)
        XCTAssertNoThrow(soundManager.playMoveSound(for: promotionMove))
        
        // Promotion should not be treated as a capture (unless it's a capture promotion)
        XCTAssertFalse(promotion.result.isCapture)
    }
    
    func testPromotionWithCapture() {
        let capturedPiece = Piece(.rook, color: .black, square: .a1)
        let capturePromotion = MetaMove(
            result: .capture(capturedPiece),
            piece: Piece(.pawn, color: .white, square: .a1),
            start: Square.e7,
            end: Square.f8
        )
        
        let capturePromotionMove = Move(metaMove: capturePromotion)
        XCTAssertNoThrow(soundManager.playMoveSound(for: capturePromotionMove))
        
        // Capture promotion should be treated as a capture
        XCTAssertTrue(capturePromotion.result.isCapture)
    }
    
    // MARK: - Performance Tests
    
    func testSoundPlayingPerformance() {
        let from = Square.e2
        let to = Square.e4
        let piece = Piece(.pawn, color: .white, square: .a1)
        
        let metaMove = MetaMove(
            result: .move,
            piece: piece,
            start: from,
            end: to
        )
        
        let move = Move(metaMove: metaMove)
        
        measure {
            for _ in 0..<100 {
                soundManager.playMoveSound(for: move)
            }
        }
    }
    
    func testSoundManagerCreationPerformance() {
        measure {
            for _ in 0..<100 {
                let manager = SoundManager()
                _ = manager // Keep reference to avoid optimization
            }
        }
    }
    
    // MARK: - Memory Management Tests
    
    func testSoundManagerMemoryHandling() {
        // Test that creating and releasing sound managers doesn't leak memory
        weak var weakSoundManager: SoundManager?
        
        autoreleasepool {
            let testSoundManager = SoundManager()
            weakSoundManager = testSoundManager
            
            // Use the sound manager
            let move = createTestMove()
            testSoundManager.playMoveSound(for: move)
        }
        
        // Give time for cleanup
        let expectation = XCTestExpectation(description: "Memory cleanup")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: 1.0)
        
        // Sound manager should be deallocated
        XCTAssertNil(weakSoundManager, "SoundManager should be deallocated")
    }
    
    // MARK: - Edge Cases Tests
    
    func testConcurrentSoundPlaying() {
        let moves = [
            createTestMove(isCapture: false),
            createTestMove(isCapture: true),
            createTestMove(isCapture: false),
            createTestMove(isCapture: true)
        ]
        
        let expectation = XCTestExpectation(description: "Concurrent sound playing")
        expectation.expectedFulfillmentCount = moves.count
        
        // Test concurrent sound playing on main queue to avoid Sendable issues
        for move in moves {
            DispatchQueue.main.async {
                self.soundManager.playMoveSound(for: move)
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: 2.0)
    }
    
    func testRapidSoundPlaying() {
        let move = createTestMove()
        
        // Play the same sound rapidly
        for _ in 0..<50 {
            soundManager.playMoveSound(for: move)
        }
        
        // Should not crash
        XCTAssertTrue(true)
    }
    
    // MARK: - Helper Methods
    
    private func createTestMove(isCapture: Bool = false) -> Move {
        let piece = Piece(.pawn, color: .white, square: .a1)
        let result: MetaMove.Result
        
        if isCapture {
            let capturedPiece = Piece(.pawn, color: .black, square: .a1)
            result = .capture(capturedPiece)
        } else {
            result = .move
        }
        
        let metaMove = MetaMove(
            result: result,
            piece: piece,
            start: Square.e2,
            end: Square.e4
        )
        
        return Move(metaMove: metaMove)
    }
}

// MARK: - Test Extensions

extension SoundManagerTests {
    
    /// Test that sound type enum correctly identifies all move result types
    func testSoundTypeForAllMoveResults() {
        let testPiece = Piece(.pawn, color: .white, square: .a1)
        let capturedPiece = Piece(.pawn, color: .black, square: .a1)
        
        let moveResults: [MetaMove.Result] = [
            .move,
            .capture(capturedPiece)
        ]
        
        for result in moveResults {
            let metaMove = MetaMove(
                result: result,
                piece: testPiece,
                start: Square.e2,
                end: Square.e4
            )
            
            let move = Move(metaMove: metaMove)
            
            // Should not crash for any move type
            XCTAssertNoThrow(soundManager.playMoveSound(for: move))
            
            // Verify isCapture detection
            let expectedIsCapture = switch result {
            case .capture:
                true
            case .move:
                false
            case .castle:
                false
            }
            
            XCTAssertEqual(result.isCapture, expectedIsCapture, "isCapture detection failed for \(result)")
        }
    }
}

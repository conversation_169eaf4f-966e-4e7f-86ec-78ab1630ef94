//
//  PositionEditorViewModelTests.swift
//  MacChessBaseTests
//
//  Tests for Position Editor functionality
//

import XCTest
@testable import Mac<PERSON>hessBase
@testable import ChessKit

final class PositionEditorViewModelTests: XCTestCase {
    
    var viewModel: PositionEditorViewModel!
    
    @MainActor
    override func setUp() {
        super.setUp()
        viewModel = PositionEditorViewModel()
    }
    
    override func tearDown() {
        viewModel = nil
        super.tearDown()
    }
    
    // MARK: - Initialization Tests
    
    @MainActor
    func testInitialState() {
        XCTAssertEqual(viewModel.position, Position.standard)
        XCTAssertEqual(viewModel.fenString, Position.standard.fen)
        XCTAssertEqual(viewModel.initialNumber, 1)
        XCTAssertTrue(viewModel.isCurrentFENValid)
        XCTAssertFalse(viewModel.isBoardFlipped)
    }
    
    // MARK: - FEN String Tests
    
    @MainActor
    func testFENStringUpdatesPosition() {
        let testFEN = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        viewModel.fenString = testFEN
        viewModel.updateFromFENTest()
        
        XCTAssertEqual(viewModel.position.fen, testFEN)
        XCTAssertEqual(viewModel.position.sideToMove, .black)
        XCTAssertEqual(viewModel.position.piece(at: .e4)?.kind, .pawn)
        XCTAssertEqual(viewModel.position.piece(at: .e4)?.color, .white)
    }
    
    @MainActor
    func testInvalidFENString() {
        let invalidFEN = "invalid_fen_string"
        viewModel.fenString = invalidFEN
        
        XCTAssertFalse(viewModel.isCurrentFENValid)
        // Position should remain unchanged from previous valid state
    }
    
    @MainActor
    func testFENValidationStatus() {
        // Valid FEN
        let validFEN = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        viewModel.fenString = validFEN
        XCTAssertTrue(viewModel.isCurrentFENValid)
        
        // Invalid FEN - missing king
        let invalidFEN = "rnbq1bnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        viewModel.fenString = invalidFEN
        XCTAssertFalse(viewModel.isCurrentFENValid)
    }
    
    // MARK: - Initial Move Number Tests
    
    @MainActor
    func testInitialMoveNumber() {
        XCTAssertEqual(viewModel.initialNumber, 1)
        
        viewModel.initialNumber = 50
        XCTAssertEqual(viewModel.initialNumber, 50)
        
        // Test minimum value
        viewModel.initialNumber = 0
        XCTAssertEqual(viewModel.initialNumber, 1) // Should be clamped to 1
    }
    
    @MainActor
    func testInitialMoveNumberUpdatesFEN() {
        viewModel.initialNumber = 25
        
        let fenComponents = viewModel.fenString.components(separatedBy: " ")
        XCTAssertEqual(fenComponents.last, "25")
    }
    
    // MARK: - Castling Rights Tests
    
    @MainActor
    func testCastlingRightsToggle() {
        viewModel.whiteCanCastleKingSide = false
        XCTAssertFalse(viewModel.whiteCanCastleKingSide)
        
        viewModel.whiteCanCastleKingSide = true
        XCTAssertTrue(viewModel.whiteCanCastleKingSide)
        
        viewModel.blackCanCastleQueenSide = false
        XCTAssertFalse(viewModel.blackCanCastleQueenSide)
        
        viewModel.blackCanCastleQueenSide = true
        XCTAssertTrue(viewModel.blackCanCastleQueenSide)
    }
    
    // MARK: - Side to Move Tests
    
    @MainActor
    func testSideToMoveToggle() {
        XCTAssertEqual(viewModel.sideToMove, .white)
        
        viewModel.sideToMove = .black
        XCTAssertEqual(viewModel.sideToMove, .black)
        
        viewModel.sideToMove = .white
        XCTAssertEqual(viewModel.sideToMove, .white)
    }
    
    // MARK: - Board Operations Tests
    
    @MainActor
    func testResetToStandardPosition() {
        // Modify position first
        viewModel.fenString = "8/8/8/8/8/8/8/8 w - - 0 1"
        viewModel.initialNumber = 50
        
        viewModel.resetToStandardPosition()
        viewModel.updateFromFENTest()
        
        XCTAssertEqual(viewModel.position, Position.standard)
        XCTAssertEqual(viewModel.initialNumber, 1)
        XCTAssertTrue(viewModel.isCurrentFENValid)
    }
    
    @MainActor
    func testClearBoard() {
        viewModel.clearBoard()
        viewModel.updateFromFENTest()
        
        XCTAssertTrue(viewModel.position.pieces.isEmpty)
        XCTAssertEqual(viewModel.position.sideToMove, .white)
        XCTAssertEqual(viewModel.position.clock.halfmoves, 0)
        XCTAssertEqual(viewModel.position.clock.fullmoves, 1)
    }
    
    // MARK: - Position Validation Tests
    
    @MainActor
    func testPositionValidation() {
        // Valid position
        XCTAssertTrue(viewModel.isCurrentFENValid)
        
        // Create invalid position - no kings
        viewModel.fenString = "8/8/8/8/8/8/8/8 w - - 0 1"
        XCTAssertFalse(viewModel.isCurrentFENValid)
        
        // Create position with kings
        viewModel.fenString = "4k3/8/8/8/8/8/8/4K3 w - - 0 1"
        XCTAssertTrue(viewModel.isCurrentFENValid)
    }
    
    // MARK: - Edge Cases Tests
    
    @MainActor
    func testSquareInteraction() {
        // Set delete tool
        viewModel.selectedTool = .delete
        
        // Tap on a square with a piece (should delete it)
        viewModel.handleSquareTap(.e2)
        
        // Verify piece was removed
        XCTAssertNil(viewModel.position.piece(at: .e2))
    }
    
    @MainActor
    func testComplexPositionEditing() {
        // Start with empty board
        viewModel.clearBoard()
        viewModel.updateFromFENTest()
        
        // Should have empty position
        XCTAssertTrue(viewModel.position.pieces.isEmpty)
        
        // Reset to standard
        viewModel.resetToStandardPosition()
        viewModel.updateFromFENTest()
        
        // Should be back to standard position
        XCTAssertEqual(viewModel.position, Position.standard)
        XCTAssertTrue(viewModel.isCurrentFENValid)
    }
    
    // MARK: - Right Click Tests
    
    @MainActor
    func testRightClickWithPieceTool() {
        // Set up a white pawn tool
        let whitePawn = Piece(.pawn, color: .white, square: .a1)
        viewModel.selectedTool = .piece(whitePawn)
        
        // Start with empty board
        viewModel.clearBoard()
        viewModel.updateFromFENTest()
        
        // Right-click on empty square should place opposite color piece (black pawn)
        viewModel.handleRightClick(.e4)
        
        // Verify black pawn was placed
        let placedPiece = viewModel.position.piece(at: .e4)
        XCTAssertNotNil(placedPiece)
        XCTAssertEqual(placedPiece?.kind, .pawn)
        XCTAssertEqual(placedPiece?.color, .black)
    }
    
    @MainActor
    func testRightClickWithPieceToolOnExistingPiece() {
        // Set up a white queen tool
        let whiteQueen = Piece(.queen, color: .white, square: .a1)
        viewModel.selectedTool = .piece(whiteQueen)
        
        // Start with standard position (has pieces)
        viewModel.resetToStandardPosition()
        viewModel.updateFromFENTest()
        
        // Right-click on a square with white pawn (e2) should place black queen
        viewModel.handleRightClick(.e2)
        
        // Verify black queen was placed (replacing white pawn)
        let placedPiece = viewModel.position.piece(at: .e2)
        XCTAssertNotNil(placedPiece)
        XCTAssertEqual(placedPiece?.kind, .queen)
        XCTAssertEqual(placedPiece?.color, .black)
    }
    
    @MainActor
    func testRightClickWithPieceToolOnSameOppositeColorPiece() {
        // Set up a white rook tool
        let whiteRook = Piece(.rook, color: .white, square: .a1)
        viewModel.selectedTool = .piece(whiteRook)
        
        // Start with empty board and place a black rook
        viewModel.clearBoard()
        viewModel.updateFromFENTest()
        
        // First, place a black rook by normal left-click with black rook tool
        let blackRook = Piece(.rook, color: .black, square: .a1)
        viewModel.selectedTool = .piece(blackRook)
        viewModel.handleSquareTap(.d4)
        
        // Verify black rook is placed
        XCTAssertEqual(viewModel.position.piece(at: .d4)?.color, .black)
        XCTAssertEqual(viewModel.position.piece(at: .d4)?.kind, .rook)
        
        // Now switch to white rook tool and right-click on the black rook
        viewModel.selectedTool = .piece(whiteRook)
        viewModel.handleRightClick(.d4)
        
        // Right-click should try to place opposite color (black rook)
        // Since there's already a black rook there, it should be removed
        XCTAssertNil(viewModel.position.piece(at: .d4))
    }
    
    @MainActor
    func testRightClickWithDeleteTool() {
        // Set up delete tool
        viewModel.selectedTool = .delete
        
        // Start with standard position
        viewModel.resetToStandardPosition()
        viewModel.updateFromFENTest()
        
        // Verify there's a piece at e2
        XCTAssertNotNil(viewModel.position.piece(at: .e2))
        
        // Right-click with delete tool should behave same as left-click (delete)
        viewModel.handleRightClick(.e2)
        
        // Verify piece was removed
        XCTAssertNil(viewModel.position.piece(at: .e2))
    }
    
    @MainActor
    func testRightClickWithDeleteToolOnEmptySquare() {
        // Set up delete tool
        viewModel.selectedTool = .delete
        
        // Start with empty board
        viewModel.clearBoard()
        viewModel.updateFromFENTest()
        
        // Right-click on empty square with delete tool should do nothing
        viewModel.handleRightClick(.e4)
        
        // Verify square is still empty
        XCTAssertNil(viewModel.position.piece(at: .e4))
        XCTAssertTrue(viewModel.position.pieces.isEmpty)
    }
    
    @MainActor
    func testRightClickAllPieceTypes() {
        // Test right-click behavior for all piece types
        let pieceTypes: [Piece.Kind] = [.pawn, .knight, .bishop, .rook, .queen, .king]
        
        for pieceType in pieceTypes {
            // Start with empty board
            viewModel.clearBoard()
            viewModel.updateFromFENTest()
            
            // Set white piece tool
            let whitePiece = Piece(pieceType, color: .white, square: .a1)
            viewModel.selectedTool = .piece(whitePiece)
            
            // Right-click should place black piece
            let testSquare = Square.e4
            viewModel.handleRightClick(testSquare)
            
            // Verify opposite color piece was placed
            let placedPiece = viewModel.position.piece(at: testSquare)
            XCTAssertNotNil(placedPiece, "Failed to place piece for type: \(pieceType)")
            XCTAssertEqual(placedPiece?.kind, pieceType, "Wrong piece type placed for: \(pieceType)")
            XCTAssertEqual(placedPiece?.color, .black, "Wrong color placed for type: \(pieceType)")
        }
    }
    
    @MainActor
    func testRightClickOnAllSquares() {
        // Test right-click behavior on all squares of the board
        let whitePawn = Piece(.pawn, color: .white, square: .a1)
        viewModel.selectedTool = .piece(whitePawn)
        
        // Start with empty board
        viewModel.clearBoard()
        viewModel.updateFromFENTest()
        
        // Right-click on all squares
        for file in Square.File.allCases {
            for rank in Square.Rank.range {
                let square = Square("\(file)\(rank)")
                viewModel.handleRightClick(square)
                
                // Verify black pawn was placed
                let placedPiece = viewModel.position.piece(at: square)
                XCTAssertNotNil(placedPiece, "Failed to place piece at: \(square)")
                XCTAssertEqual(placedPiece?.kind, .pawn, "Wrong piece type at: \(square)")
                XCTAssertEqual(placedPiece?.color, .black, "Wrong color at: \(square)")
            }
        }
        
        // Verify all 64 squares have black pawns
        XCTAssertEqual(viewModel.position.pieces.count, 64)
        XCTAssertTrue(viewModel.position.pieces.allSatisfy { $0.kind == .pawn && $0.color == .black })
    }
    
    // MARK: - Performance Tests
    
    @MainActor
    func testPositionValidationPerformance() {
        measure {
            for _ in 0..<100 {
                _ = viewModel.isCurrentFENValid
            }
        }
    }
    
    @MainActor
    func testFENStringUpdatePerformance() {
        let testFEN = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
        
        measure {
            for _ in 0..<100 {
                viewModel.fenString = testFEN
            }
        }
    }
}
